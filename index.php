<?php
require_once 'includes/functions.php';

$pageTitle = 'Home';
$pageDescription = 'Professional construction services in London. Civil Engineering, Groundworks, RC Frames, Basements, and Hard Landscaping by Flori Construction Ltd.';

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-slider">
        <div class="hero-slide active" style="background-image: url('assets/images/hero-bg-1.jpg');">
            <div class="hero-overlay"></div>
            <div class="container">
                <div class="row align-items-center min-vh-100">
                    <div class="col-lg-8">
                        <div class="hero-content text-white" data-aos="fade-up">
                            <h1 class="display-4 fw-bold mb-4">DO IT WITH PASSION<br><span class="text-primary">or not at all</span></h1>
                            <p class="lead mb-4">The only way to do great work is to love what you do. We take pride in our work and deliver high-quality construction solutions.</p>
                            <div class="hero-buttons">
                                <a href="projects.php" class="btn btn-primary btn-lg me-3">View Our Projects</a>
                                <a href="contact.php" class="btn btn-outline-light btn-lg">Get Quote</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- About Section -->
<section class="about-section py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6" data-aos="fade-right">
                <div class="about-content">
                    <h2 class="section-title mb-4">About Our Company</h2>
                    <p class="lead">Flori Construction Ltd began its journey in the heart of London, founded on the principles of quality, professionalism, and an unwavering dedication to customer satisfaction.</p>
                    <p>From our humble beginnings, we have grown exponentially, establishing ourselves as a premier construction company renowned for our exceptional services in Civil Engineering, Professional Groundworks, RC Frames Construction, Basement Construction, and Hard Landscaping.</p>
                    <div class="row mt-4">
                        <div class="col-6">
                            <div class="stat-item text-center">
                                <h3 class="text-primary">100+</h3>
                                <p>Projects Completed</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item text-center">
                                <h3 class="text-primary">15+</h3>
                                <p>Years Experience</p>
                            </div>
                        </div>
                    </div>
                    <a href="about.php" class="btn btn-primary mt-4">Learn More</a>
                </div>
            </div>
            <div class="col-lg-6" data-aos="fade-left">
                <div class="about-image">
                    <img src="assets/images/about-image.jpg" alt="About Flori Construction" class="img-fluid rounded shadow">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="services-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">Services We Provide</h2>
                <p class="lead">We are committed to delivering high-quality services in these areas and more. Our goal is to provide reliable and efficient solutions that meet the unique needs of each project.</p>
            </div>
        </div>
        <div class="row">
            <?php
            $services = getServices('active');
            foreach (array_slice($services, 0, 5) as $index => $service) {
                $delay = $index * 100;
                echo '<div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="' . $delay . '">';
                echo '<div class="service-card h-100">';
                echo '<div class="service-icon">';
                echo '<i class="' . ($service['icon'] ?: 'fas fa-tools') . '"></i>';
                echo '</div>';
                echo '<h4>' . htmlspecialchars($service['title']) . '</h4>';
                echo '<p>' . htmlspecialchars(substr($service['description'], 0, 150)) . '...</p>';
                echo '<a href="service-detail.php?id=' . $service['id'] . '" class="btn btn-outline-primary">Learn More</a>';
                echo '</div>';
                echo '</div>';
            }
            ?>
        </div>
        <div class="text-center mt-4">
            <a href="services.php" class="btn btn-primary">View All Services</a>
        </div>
    </div>
</section>

<!-- Projects Section -->
<section class="projects-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">Latest Projects</h2>
                <p class="lead">Every Project is Unique and Custom Made</p>
            </div>
        </div>
        <div class="row">
            <?php
            $recentProjects = getProjects(null, 6);
            foreach ($recentProjects as $index => $project) {
                $delay = $index * 100;
                echo '<div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="' . $delay . '">';
                echo '<div class="project-card">';
                echo '<div class="project-image">';
                echo '<img src="' . ($project['featured_image'] ?: 'assets/images/project-placeholder.jpg') . '" alt="' . htmlspecialchars($project['title']) . '" class="img-fluid">';
                echo '<div class="project-overlay">';
                echo '<a href="project-detail.php?id=' . $project['id'] . '" class="btn btn-light">View Details</a>';
                echo '</div>';
                echo '</div>';
                echo '<div class="project-content">';
                echo '<span class="project-status badge bg-' . ($project['status'] == 'completed' ? 'success' : 'warning') . '">' . ucfirst($project['status']) . '</span>';
                echo '<h4>' . htmlspecialchars($project['title']) . '</h4>';
                echo '<p class="text-muted">' . htmlspecialchars($project['location']) . '</p>';
                echo '</div>';
                echo '</div>';
                echo '</div>';
            }
            ?>
        </div>
        <div class="text-center mt-4">
            <a href="projects.php" class="btn btn-primary">View All Projects</a>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8" data-aos="fade-right">
                <h2 class="mb-3">Ready to Start Your Project?</h2>
                <p class="lead mb-0">We Collaborate with Ambitious Brands and People; We'd Love to Build Something Great Together.</p>
            </div>
            <div class="col-lg-4 text-lg-end" data-aos="fade-left">
                <a href="contact.php" class="btn btn-light btn-lg">Get Free Quote</a>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
